# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint, flt, cstr
import json


@frappe.whitelist(allow_guest=True)
def get_listings(filters=None, page=1, page_size=20, sort_by="creation", sort_order="desc"):
	"""Get listings with filters and pagination"""
	try:
		# Parse filters if string
		if isinstance(filters, str):
			filters = json.loads(filters)
		
		if not filters:
			filters = {}
		
		# Convert page and page_size to int
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		# Base filters for active listings
		base_filters = {
			"status": "Active",
			"expires_on": [">=", frappe.utils.today()]
		}
		
		# Add custom filters
		if filters.get("category"):
			base_filters["category"] = filters["category"]
		
		if filters.get("listing_type"):
			base_filters["listing_type"] = filters["listing_type"]
		
		if filters.get("location"):
			base_filters["location"] = ["like", f"%{filters['location']}%"]
		
		if filters.get("min_price"):
			base_filters["price"] = [">=", flt(filters["min_price"])]
		
		if filters.get("max_price"):
			if "price" in base_filters:
				base_filters["price"] = ["between", [flt(filters["min_price"]), flt(filters["max_price"])]]
			else:
				base_filters["price"] = ["<=", flt(filters["max_price"])]
		
		if filters.get("condition"):
			base_filters["condition"] = filters["condition"]
		
		# Search query
		if filters.get("search"):
			search_query = filters["search"]
			# Use OR condition for title and description search
			base_filters["title"] = ["like", f"%{search_query}%"]
		
		# Get total count
		total = frappe.db.count("Listing", base_filters)
		
		# Get listings
		fields = [
			"name", "title", "description", "price", "currency", "location", 
			"category", "listing_type", "condition", "creation", "views_count",
			"featured", "created_by_user"
		]
		
		listings = frappe.get_all(
			"Listing",
			filters=base_filters,
			fields=fields,
			order_by=f"{sort_by} {sort_order}",
			start=(page - 1) * page_size,
			page_length=page_size
		)
		
		# Add primary images and additional info
		for listing in listings:
			# Get primary image
			primary_image = frappe.db.get_value(
				"Listing Image",
				{"parent": listing.name, "is_primary": 1},
				"image"
			)
			listing["primary_image"] = primary_image
			
			# Get category name
			listing["category_name"] = frappe.db.get_value("Category", listing.category, "category_name")
			
			# Get user name
			listing["user_name"] = frappe.db.get_value("User", listing.created_by_user, "full_name")
		
		return {
			"success": True,
			"data": {
				"listings": listings,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_listings: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def get_listing(listing_id):
	"""Get single listing with full details"""
	try:
		# Check if listing exists and is accessible
		listing = frappe.get_doc("Listing", listing_id)
		
		if listing.status != "Active":
			return {"success": False, "error": "Listing not found or not active"}
		
		# Increment view count
		listing.increment_views()
		
		# Get listing data
		listing_data = listing.as_dict()
		
		# Get images
		images = frappe.get_all(
			"Listing Image",
			filters={"parent": listing_id},
			fields=["image", "caption", "is_primary"],
			order_by="is_primary desc, idx asc"
		)
		listing_data["images"] = images
		
		# Get category info
		category_info = frappe.get_value(
			"Category",
			listing.category,
			["category_name", "icon"],
			as_dict=True
		)
		listing_data["category_info"] = category_info
		
		# Get user info
		user_info = frappe.get_value(
			"User",
			listing.created_by_user,
			["full_name", "user_image"],
			as_dict=True
		)
		listing_data["user_info"] = user_info
		
		# Get user profile info
		user_profile = frappe.db.exists("User Profile", listing.created_by_user)
		if user_profile:
			profile_info = frappe.get_value(
				"User Profile",
				user_profile,
				["rating", "total_ratings", "verification_status", "location"],
				as_dict=True
			)
			listing_data["user_profile"] = profile_info
		
		# Get similar listings
		similar_listings = listing.get_similar_listings(5)
		listing_data["similar_listings"] = similar_listings
		
		return {"success": True, "data": listing_data}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in get_listing: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def create_listing(**kwargs):
	"""Create new listing"""
	try:
		# Check if user is logged in
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Create listing document
		listing = frappe.get_doc({
			"doctype": "Listing",
			**kwargs
		})
		
		listing.insert()
		
		return {
			"success": True,
			"message": "Listing created successfully",
			"data": {"listing_id": listing.name}
		}
		
	except frappe.ValidationError as e:
		return {"success": False, "error": str(e)}
	except Exception as e:
		frappe.log_error(f"Error in create_listing: {str(e)}")
		return {"success": False, "error": "Failed to create listing"}


@frappe.whitelist()
def update_listing(listing_id, **kwargs):
	"""Update existing listing"""
	try:
		# Check if user is logged in
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get listing
		listing = frappe.get_doc("Listing", listing_id)
		
		# Check permissions
		if not listing.can_edit():
			return {"success": False, "error": "Permission denied"}
		
		# Update fields
		for key, value in kwargs.items():
			if hasattr(listing, key):
				setattr(listing, key, value)
		
		listing.save()
		
		return {"success": True, "message": "Listing updated successfully"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in update_listing: {str(e)}")
		return {"success": False, "error": "Failed to update listing"}


@frappe.whitelist()
def delete_listing(listing_id):
	"""Delete listing"""
	try:
		# Check if user is logged in
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get listing
		listing = frappe.get_doc("Listing", listing_id)
		
		# Check permissions
		if not listing.can_delete():
			return {"success": False, "error": "Permission denied"}
		
		listing.delete()
		
		return {"success": True, "message": "Listing deleted successfully"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in delete_listing: {str(e)}")
		return {"success": False, "error": "Failed to delete listing"}


@frappe.whitelist(allow_guest=True)
def get_featured_listings(limit=6):
	"""Get featured listings"""
	try:
		limit = cint(limit) or 6
		
		listings = frappe.get_all(
			"Listing",
			filters={
				"status": "Active",
				"featured": 1,
				"expires_on": [">=", frappe.utils.today()]
			},
			fields=[
				"name", "title", "price", "currency", "location", 
				"category", "creation", "views_count"
			],
			order_by="creation desc",
			limit=limit
		)
		
		# Add primary images and category names
		for listing in listings:
			primary_image = frappe.db.get_value(
				"Listing Image",
				{"parent": listing.name, "is_primary": 1},
				"image"
			)
			listing["primary_image"] = primary_image
			listing["category_name"] = frappe.db.get_value("Category", listing.category, "category_name")
		
		return {"success": True, "data": listings}
		
	except Exception as e:
		frappe.log_error(f"Error in get_featured_listings: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_recent_listings(limit=8):
	"""Get recent listings"""
	try:
		limit = cint(limit) or 8
		
		listings = frappe.get_all(
			"Listing",
			filters={
				"status": "Active",
				"expires_on": [">=", frappe.utils.today()]
			},
			fields=[
				"name", "title", "price", "currency", "location", 
				"category", "creation", "views_count"
			],
			order_by="creation desc",
			limit=limit
		)
		
		# Add primary images and category names
		for listing in listings:
			primary_image = frappe.db.get_value(
				"Listing Image",
				{"parent": listing.name, "is_primary": 1},
				"image"
			)
			listing["primary_image"] = primary_image
			listing["category_name"] = frappe.db.get_value("Category", listing.category, "category_name")
		
		return {"success": True, "data": listings}
		
	except Exception as e:
		frappe.log_error(f"Error in get_recent_listings: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def get_user_listings(user=None, status=None, page=1, page_size=20):
	"""Get listings for specific user"""
	try:
		if not user:
			user = frappe.session.user
		
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Check permissions
		if user != frappe.session.user and "DMSoko Admin" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		filters = {"created_by_user": user}
		if status:
			filters["status"] = status
		
		# Get total count
		total = frappe.db.count("Listing", filters)
		
		# Get listings
		listings = frappe.get_all(
			"Listing",
			filters=filters,
			fields=[
				"name", "title", "price", "currency", "location", 
				"category", "status", "creation", "views_count", "expires_on"
			],
			order_by="creation desc",
			start=(page - 1) * page_size,
			page_length=page_size
		)
		
		# Add primary images and category names
		for listing in listings:
			primary_image = frappe.db.get_value(
				"Listing Image",
				{"parent": listing.name, "is_primary": 1},
				"image"
			)
			listing["primary_image"] = primary_image
			listing["category_name"] = frappe.db.get_value("Category", listing.category, "category_name")
		
		return {
			"success": True,
			"data": {
				"listings": listings,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_user_listings: {str(e)}")
		return {"success": False, "error": str(e)}
