<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMSoko Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="container mx-auto px-4 py-4">
                <h1 class="text-2xl font-bold text-blue-600">DMSoko Test Page</h1>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto px-4 py-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Welcome to DMSoko</h2>
                <p class="text-xl text-gray-600 mb-8">Your local marketplace for buying and selling</p>
            </div>

            <!-- Listings Section -->
            <section class="mb-12">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Recent Listings</h3>
                <div id="listings-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="text-center py-8">
                        <p class="text-gray-600">Loading listings...</p>
                    </div>
                </div>
            </section>

            <!-- Test Button -->
            <div class="text-center">
                <button onclick="loadListings()" class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700">
                    Load Listings
                </button>
            </div>
        </main>
    </div>

    <script>
        // Function to format price
        function formatPrice(price) {
            if (!price) return 'Price on request';
            return new Intl.NumberFormat('en-TZ', {
                style: 'currency',
                currency: 'TZS',
                minimumFractionDigits: 0
            }).format(price);
        }

        // Function to load listings
        async function loadListings() {
            console.log('Loading listings...');
            const container = document.getElementById('listings-container');
            container.innerHTML = '<div class="col-span-full text-center py-8"><p class="text-gray-600">Loading...</p></div>';
            
            try {
                const response = await fetch('/api/method/dmsoko.api.listings.get_all_listings');
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.message && data.message.success) {
                    const listings = data.message.data.slice(0, 6);
                    console.log('Listings:', listings);
                    
                    if (listings.length === 0) {
                        container.innerHTML = '<div class="col-span-full text-center py-8"><p class="text-gray-600">No listings found.</p></div>';
                        return;
                    }
                    
                    container.innerHTML = listings.map(listing => `
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <div class="p-6">
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">${listing.title}</h4>
                                <p class="text-gray-600 mb-3">${listing.description}</p>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xl font-bold text-blue-600">${formatPrice(listing.price)}</span>
                                    <span class="text-sm text-gray-500">${listing.location}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">${listing.category}</span>
                                    <span class="text-xs text-gray-500">${new Date(listing.creation).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    throw new Error('API returned error: ' + JSON.stringify(data));
                }
            } catch (error) {
                console.error('Error loading listings:', error);
                container.innerHTML = `<div class="col-span-full text-center py-8"><p class="text-red-600">Error: ${error.message}</p></div>`;
            }
        }

        // Auto-load listings when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, auto-loading listings');
            loadListings();
        });
    </script>
</body>
</html>
