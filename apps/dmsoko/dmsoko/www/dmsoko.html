<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "DMSoko - Your Marketplace" }}</title>
    <meta name="description" content="DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta name="keywords" content="classified ads, marketplace, buy, sell, local, cars, jobs, real estate">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="og:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="og:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="og:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="twitter:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="twitter:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="twitter:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/dmsoko/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/dmsoko/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/dmsoko/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/dmsoko/images/favicon-16x16.png">

    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/dmsoko/css/dmsoko.css" as="style">
    <link rel="preload" href="/assets/dmsoko/js/dmsoko.js" as="script">

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/frappe/css/frappe-web.css">
    <link rel="stylesheet" href="/assets/dmsoko/css/dmsoko.css">
    
    <!-- Tailwind CSS CDN for development -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Basic CSS for fallback -->
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>

    <!-- Frappe Framework -->
    <script>
        window.frappe = window.frappe || {};
        window.frappe.ready_events = [];
        window.frappe.ready = function(fn) {
            window.frappe.ready_events.push(fn);
        };
        
        // Frappe session data
        window.frappe.session = {
            user: "{{ frappe.session.user }}",
            user_fullname: "{{ frappe.session.data.full_name or '' }}",
            user_image: "{{ frappe.session.data.user_image or '' }}",
            csrf_token: "{{ frappe.session.csrf_token }}"
        };
        
        // Frappe call function
        window.frappe.call = function(opts) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/method/' + opts.method);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response'));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(opts.args || {}));
            });
        };
    </script>
</head>
<body class="bg-gray-50">
    <!-- Main Content -->
    <div id="main-content">
        <!-- Content will be loaded by JavaScript -->
    </div>

    <!-- Fallback content for users without JavaScript -->
    <noscript>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="max-w-md mx-auto text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">JavaScript Required</h1>
                <p class="text-gray-600 mb-4">
                    DMSoko requires JavaScript to function properly. Please enable JavaScript in your browser and refresh the page.
                </p>
                <a href="/dmsoko" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    Refresh Page
                </a>
            </div>
        </div>
    </noscript>

    <!-- Scripts -->
    <script src="/assets/frappe/js/frappe-web.min.js"></script>
    <script src="/assets/dmsoko/js/dmsoko.js"></script>
    
    <!-- Initialize App -->
    <script>
        // Function to format price
        function formatPrice(price) {
            if (!price) return 'Price on request';
            return new Intl.NumberFormat('en-TZ', {
                style: 'currency',
                currency: 'TZS',
                minimumFractionDigits: 0
            }).format(price);
        }

        // Function to load listings
        async function loadListings() {
            try {
                const response = await fetch('/api/method/dmsoko.api.listings.get_all_listings');
                const data = await response.json();

                if (data.message && data.message.success) {
                    const listings = data.message.data.slice(0, 6); // Show first 6 listings
                    const container = document.getElementById('listings-container');

                    if (listings.length === 0) {
                        container.innerHTML = '<div class="col-span-full text-center py-8"><p class="text-gray-600">No listings found.</p></div>';
                        return;
                    }

                    container.innerHTML = listings.map(listing => `
                        <div class="bg-white rounded-lg shadow-md overflow-hidden">
                            <div class="p-6">
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">${listing.title}</h4>
                                <p class="text-gray-600 mb-3 line-clamp-2">${listing.description}</p>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xl font-bold text-blue-600">${formatPrice(listing.price)}</span>
                                    <span class="text-sm text-gray-500">${listing.location}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">${listing.category}</span>
                                    <span class="text-xs text-gray-500">${new Date(listing.creation).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    throw new Error('Failed to load listings');
                }
            } catch (error) {
                console.error('Error loading listings:', error);
                const container = document.getElementById('listings-container');
                container.innerHTML = '<div class="col-span-full text-center py-8"><p class="text-red-600">Failed to load listings. Please refresh the page.</p></div>';
            }
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing DMSoko');
            showMainContent();
        });

        // Function to show main content
        function showMainContent() {
            console.log('Showing main content');
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="min-h-screen bg-gray-50">
                    <!-- Header -->
                    <header class="bg-white shadow-sm">
                        <div class="container mx-auto px-4 py-4">
                            <div class="flex items-center justify-between">
                                <h1 class="text-2xl font-bold text-blue-600">DMSoko</h1>
                                <nav class="space-x-6">
                                    <a href="#" class="text-gray-600 hover:text-blue-600">Home</a>
                                    <a href="#" class="text-gray-600 hover:text-blue-600">Browse</a>
                                    <a href="#" class="text-gray-600 hover:text-blue-600">Post Ad</a>
                                </nav>
                            </div>
                        </div>
                    </header>

                    <!-- Main Content -->
                    <main class="container mx-auto px-4 py-8">
                        <div class="text-center mb-12">
                            <h2 class="text-4xl font-bold text-gray-900 mb-4">Welcome to DMSoko</h2>
                            <p class="text-xl text-gray-600 mb-8">Your local marketplace for buying and selling</p>
                        </div>

                        <!-- Listings Section -->
                        <section class="mb-12">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Recent Listings</h3>
                            <div id="listings-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div class="text-center py-8">
                                    <p class="text-gray-600">Loading listings...</p>
                                </div>
                            </div>
                        </section>

                        <!-- Features Section -->
                        <section class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-semibold mb-2">Browse Listings</h4>
                                <p class="text-gray-600">Find great deals in your area</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-semibold mb-2">Post an Ad</h4>
                                <p class="text-gray-600">Sell your items quickly</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow-md text-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-semibold mb-2">Safe Trading</h4>
                                <p class="text-gray-600">Secure transactions</p>
                            </div>
                        </section>
                    </main>

                    <!-- Footer -->
                    <footer class="bg-gray-800 text-white py-8 mt-16">
                        <div class="container mx-auto px-4 text-center">
                            <p>&copy; 2025 DMSoko. All rights reserved.</p>
                        </div>
                    </footer>
                </div>
            `;

            // Load listings after content is shown
            setTimeout(loadListings, 100);
        }
    </script>
</body>
</html>
