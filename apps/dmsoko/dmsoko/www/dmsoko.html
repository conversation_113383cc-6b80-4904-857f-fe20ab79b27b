<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "DMSoko - Your Marketplace" }}</title>
    <meta name="description" content="DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta name="keywords" content="classified ads, marketplace, buy, sell, local, cars, jobs, real estate">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="og:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="og:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="og:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="twitter:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="twitter:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="twitter:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/dmsoko/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/dmsoko/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/dmsoko/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/dmsoko/images/favicon-16x16.png">

    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/dmsoko/css/dmsoko.css" as="style">
    <link rel="preload" href="/assets/dmsoko/js/dmsoko.js" as="script">

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/frappe/css/frappe-web.css">
    <link rel="stylesheet" href="/assets/dmsoko/css/dmsoko.css">
    
    <!-- Tailwind CSS CDN for development -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Vue.js CDN for development -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.js"></script>
    <script src="https://unpkg.com/pinia@2/dist/pinia.iife.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- Frappe Framework -->
    <script>
        window.frappe = window.frappe || {};
        window.frappe.ready_events = [];
        window.frappe.ready = function(fn) {
            window.frappe.ready_events.push(fn);
        };
        
        // Frappe session data
        window.frappe.session = {
            user: "{{ frappe.session.user }}",
            user_fullname: "{{ frappe.session.data.full_name or '' }}",
            user_image: "{{ frappe.session.data.user_image or '' }}",
            csrf_token: "{{ frappe.session.csrf_token }}"
        };
        
        // Frappe call function
        window.frappe.call = function(opts) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/method/' + opts.method);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response'));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(opts.args || {}));
            });
        };
    </script>
</head>
<body class="bg-gray-50">
    <!-- Loading screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="inline-block w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p class="text-gray-600">Loading DMSoko...</p>
        </div>
    </div>

    <!-- Vue.js App Container -->
    <div id="dmsoko-app" style="display: none;">
        <!-- Vue.js app will be mounted here -->
    </div>

    <!-- Fallback content for users without JavaScript -->
    <noscript>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="max-w-md mx-auto text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">JavaScript Required</h1>
                <p class="text-gray-600 mb-4">
                    DMSoko requires JavaScript to function properly. Please enable JavaScript in your browser and refresh the page.
                </p>
                <a href="/dmsoko" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    Refresh Page
                </a>
            </div>
        </div>
    </noscript>

    <!-- Scripts -->
    <script src="/assets/frappe/js/frappe-web.min.js"></script>
    <script src="/assets/dmsoko/js/dmsoko.js"></script>
    
    <!-- Initialize Vue App -->
    <script src="/assets/dmsoko/js/vue-app/main.js"></script>
    <script>
        // Hide loading screen and show app after a short delay
        setTimeout(() => {
            document.getElementById('loading-screen').style.display = 'none';
            document.getElementById('dmsoko-app').style.display = 'block';

            // Trigger Frappe ready events
            window.frappe.ready_events.forEach(fn => fn());
        }, 1000);
    </script>
</body>
</html>
