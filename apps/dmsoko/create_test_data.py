#!/usr/bin/env python3

import frappe
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_test_listings():
    """Create test listings for DMSoko"""
    
    # Initialize Frappe
    frappe.init(site='p1.site')
    frappe.connect()
    
    # Sample listing data
    listings_data = [
        {
            "title": "iPhone 14 Pro Max - Excellent Condition",
            "description": "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied. No scratches or dents. Battery health at 98%.",
            "category": "Smartphones",
            "price": 85000,
            "location": "Dar es Salaam",
            "condition": "Like New",
            "contact_phone": "+255 712 345 678",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Toyota Corolla 2018 - Low Mileage",
            "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available. Non-smoking owner. Perfect for daily commuting.",
            "category": "Cars",
            "price": 28000000,
            "location": "Arusha",
            "condition": "Good",
            "contact_phone": "+255 754 987 654",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "3 Bedroom House for Sale - Mikocheni",
            "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden. Close to schools and shopping centers.",
            "category": "Houses",
            "price": 180000000,
            "location": "Dar es Salaam",
            "condition": "Excellent",
            "contact_phone": "+255 765 432 109",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "MacBook Pro M2 - Perfect for Students",
            "description": "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals. Comes with laptop bag and wireless mouse. Still under warranty.",
            "category": "Laptops & Computers",
            "price": 2800000,
            "location": "Mwanza",
            "condition": "Like New",
            "contact_phone": "+255 678 123 456",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Modern Sofa Set - 7 Seater",
            "description": "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room. Excellent condition, no pets, no smoking household.",
            "category": "Furniture",
            "price": 850000,
            "location": "Dodoma",
            "condition": "Good",
            "contact_phone": "+255 689 765 432",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Honda CB 150R - Sport Motorcycle",
            "description": "Honda CB 150R in excellent condition. Low mileage, well maintained. Perfect for city riding and weekend trips. All documents available.",
            "category": "Motorcycles",
            "price": 4500000,
            "location": "Mbeya",
            "condition": "Excellent",
            "contact_phone": "+255 712 987 654",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Samsung 55\" Smart TV - 4K UHD",
            "description": "Samsung 55 inch Smart TV with 4K UHD resolution. Excellent picture quality, smart features work perfectly. Comes with original remote and wall mount.",
            "category": "Electronics",
            "price": 1200000,
            "location": "Dar es Salaam",
            "condition": "Good",
            "contact_phone": "+255 754 321 098",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "2 Bedroom Apartment - Masaki",
            "description": "Luxury 2 bedroom apartment in Masaki with ocean view. Fully furnished, modern amenities, swimming pool, gym, and 24/7 security.",
            "category": "Apartments",
            "price": 120000000,
            "location": "Dar es Salaam",
            "condition": "Excellent",
            "contact_phone": "+255 765 098 765",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Professional Photography Services",
            "description": "Offering professional photography services for weddings, events, portraits, and commercial shoots. 10+ years experience, high-quality equipment.",
            "category": "Professional Services",
            "price": 150000,
            "location": "Dar es Salaam",
            "condition": "New",
            "contact_phone": "+255 678 456 789",
            "contact_email": "<EMAIL>"
        },
        {
            "title": "Home Gym Equipment Set",
            "description": "Complete home gym equipment set including dumbbells, bench press, pull-up bar, and exercise bike. Perfect for home workouts. Barely used.",
            "category": "Fitness Equipment",
            "price": 750000,
            "location": "Arusha",
            "condition": "Like New",
            "contact_phone": "+255 689 123 789",
            "contact_email": "<EMAIL>"
        }
    ]
    
    created_listings = []
    
    for listing_data in listings_data:
        try:
            # Create new listing document
            listing = frappe.new_doc("Listing")
            
            # Set basic fields
            listing.title = listing_data["title"]
            listing.description = listing_data["description"]
            listing.category = listing_data["category"]
            listing.price = listing_data["price"]
            listing.location = listing_data["location"]
            listing.condition = listing_data["condition"]
            listing.contact_phone = listing_data["contact_phone"]
            listing.contact_email = listing_data["contact_email"]
            
            # Set default values
            listing.status = "Active"
            listing.listing_type = "For Sale"
            listing.owner = "Administrator"  # Use Administrator as default owner
            
            # Set creation date to random date in the last 30 days
            days_ago = random.randint(1, 30)
            listing.creation = datetime.now() - timedelta(days=days_ago)
            
            # Save the listing
            listing.insert(ignore_permissions=True)
            
            created_listings.append(listing.name)
            print(f"Created listing: {listing.title} ({listing.name})")
            
        except Exception as e:
            print(f"Error creating listing '{listing_data['title']}': {str(e)}")
            continue
    
    print(f"\nSuccessfully created {len(created_listings)} test listings!")
    return created_listings

if __name__ == "__main__":
    create_test_listings()
