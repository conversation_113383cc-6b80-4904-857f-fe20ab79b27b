2025-06-20 08:22:45,368 INFO ipython === bench console session ===
2025-06-20 08:22:45,369 INFO ipython from frappe.core.doctype.data_import.data_import import import_doc
2025-06-20 08:22:45,369 INFO ipython import_doc("apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/category.json")
2025-06-20 08:22:45,370 INFO ipython === session end ===
2025-06-20 08:25:52,010 INFO ipython === bench console session ===
2025-06-20 08:25:52,010 INFO ipython frappe.db.exists("DocType", "Category")
2025-06-20 08:25:52,011 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:25:52,011 INFO ipython frappe.get_all("DocType", filters={"module": "Listings"}, fields=["name"])
2025-06-20 08:25:52,011 INFO ipython frappe.get_installed_apps()
2025-06-20 08:25:52,011 INFO ipython === session end ===
2025-06-20 08:27:56,703 INFO ipython === bench console session ===
2025-06-20 08:27:56,703 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:27:56,703 INFO ipython frappe.get_all("DocType", filters={"app": "dmsoko"}, fields=["name", "module"])
2025-06-20 08:27:56,704 INFO ipython === session end ===
2025-06-20 09:02:36,533 INFO ipython === bench console session ===
2025-06-20 09:02:36,534 INFO ipython from frappe.modules.import_file import import_file_by_path
2025-06-20 09:02:36,534 INFO ipython import os
2025-06-20 09:02:36,534 INFO ipython # Import Category DocType
2025-06-20 09:02:36,534 INFO ipython try:
        import_file_by_path("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/listings/doctype/category/category.json", force=True)
            print("✅ Category DocType imported successfully")
2025-06-20 09:02:36,534 INFO ipython except Exception as e:
        print(f"❌ Error importing Category: {e}")
2025-06-20 09:02:36,534 INFO ipython try:
        import_file_by_path("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/listings/doctype/category/category.json", force=True)
            print("✅ Category DocType imported successfully")
2025-06-20 09:02:36,534 INFO ipython except Exception as e:
        print(f"❌ Error importing Category: {e}")
2025-06-20 09:02:36,535 INFO ipython === session end ===
2025-06-20 09:14:24,606 INFO ipython === bench console session ===
2025-06-20 09:14:24,606 INFO ipython print("Checking DMSoko DocTypes...")
2025-06-20 09:14:24,607 INFO ipython dmsoko_modules = ["Listings", "Messaging", "User Management"]
2025-06-20 09:14:24,607 INFO ipython for module in dmsoko_modules:
        doctypes = frappe.get_all("DocType", filters={"module": module}, fields=["name", "module"])
            print(f"\n{module} Module:")
2025-06-20 09:14:24,607 INFO ipython     if doctypes:
            for dt in doctypes:
                        print(f"  ✅ {dt.name}")
                        else:
                                print(f"  ❌ No DocTypes found")
2025-06-20 09:14:24,607 INFO ipython for module in ["Listings", "Messaging", "User Management"]:
        doctypes = frappe.get_all("DocType", filters={"module": module}, fields=["name", "module"])
            print(f"\n{module} Module:")
2025-06-20 09:14:24,608 INFO ipython     if doctypes:
            for dt in doctypes:
                        print(f"  ✅ {dt.name}")
                        else:
                                print(f"  ❌ No DocTypes found")
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "Listings"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "Messaging"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython frappe.get_all("DocType", filters={"module": "User Management"}, fields=["name"])
2025-06-20 09:14:24,608 INFO ipython === session end ===
2025-06-20 10:33:31,963 INFO ipython === bench console session ===
2025-06-20 10:33:31,972 INFO ipython import frappe
2025-06-20 10:33:31,973 INFO ipython frappe.db.get_list("Listing", fields=["name", "title", "status"])
2025-06-20 10:33:31,973 INFO ipython frappe.db.get_list("Category", fields=["name", "category_name"])
2025-06-20 10:33:31,973 INFO ipython === session end ===
2025-06-20 10:38:30,374 INFO ipython === bench console session ===
2025-06-20 10:38:30,374 INFO ipython import frappe
2025-06-20 10:38:30,375 INFO ipython import random
2025-06-20 10:38:30,375 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:38:30,375 INFO ipython # Sample listing data
2025-06-20 10:38:30,375 INFO ipython listings_data = [
    {
            "title": "iPhone 14 Pro Max - Excellent Condition",
                    "description": "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied. No scratches or dents. Battery health at 98%.",
                            "category": "Smartphones",
                                    "price": 85000,
                                            "location": "Dar es Salaam",
                                                    "condition": "Like New",
                                                            "contact_phone": "+255 712 345 678",
                                                                    "contact_email": "<EMAIL>"
                                                                        },
                                                                            {
                                                                                    "title": "Toyota Corolla 2018 - Low Mileage",
                                                                                            "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available. Non-smoking owner. Perfect for daily commuting.",
                                                                                                    "category": "Cars",
                                                                                                            "price": 28000000,
                                                                                                                    "location": "Arusha",
                                                                                                                            "condition": "Good",
                                                                                                                                    "contact_phone": "+255 754 987 654",
                                                                                                                                            "contact_email": "<EMAIL>"
                                                                                                                                                },
                                                                                                                                                    {
                                                                                                                                                            "title": "3 Bedroom House for Sale - Mikocheni",
                                                                                                                                                                    "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden. Close to schools and shopping centers.",
                                                                                                                                                                            "category": "Houses",
                                                                                                                                                                                    "price": 180000000,
                                                                                                                                                                                            "location": "Dar es Salaam",
                                                                                                                                                                                                    "condition": "Excellent",
                                                                                                                                                                                                            "contact_phone": "+255 765 432 109",
                                                                                                                                                                                                                    "contact_email": "<EMAIL>"
                                                                                                                                                                                                                        }
                                                                                                                                                                                                                        ]
2025-06-20 10:38:30,375 INFO ipython # Create first listing
2025-06-20 10:38:30,375 INFO ipython listing = frappe.new_doc("Listing")
2025-06-20 10:38:30,375 INFO ipython listing.title = "iPhone 14 Pro Max - Excellent Condition"
2025-06-20 10:38:30,375 INFO ipython listing.description = "Selling my iPhone 14 Pro Max in excellent condition. Used for only 6 months. Comes with original box, charger, and screen protector already applied."
2025-06-20 10:38:30,376 INFO ipython listing.category = "Smartphones"
2025-06-20 10:38:30,376 INFO ipython listing.price = 85000
2025-06-20 10:38:30,376 INFO ipython listing.location = "Dar es Salaam"
2025-06-20 10:38:30,376 INFO ipython listing.condition = "Like New"
2025-06-20 10:38:30,376 INFO ipython listing.contact_phone = "+255 712 345 678"
2025-06-20 10:38:30,377 INFO ipython listing.contact_email = "<EMAIL>"
2025-06-20 10:38:30,377 INFO ipython listing.status = "Active"
2025-06-20 10:38:30,377 INFO ipython listing.listing_type = "For Sale"
2025-06-20 10:38:30,377 INFO ipython listing.insert(ignore_permissions=True)
2025-06-20 10:38:30,377 INFO ipython print(f"Created: {listing.name}")
2025-06-20 10:38:30,377 INFO ipython # Fix the condition and try again
2025-06-20 10:38:30,378 INFO ipython listing.condition = "Used"
2025-06-20 10:38:30,378 INFO ipython listing.save(ignore_permissions=True)
2025-06-20 10:38:30,378 INFO ipython print(f"Created: {listing.name}")
2025-06-20 10:38:30,378 INFO ipython # Create more listings
2025-06-20 10:38:30,378 INFO ipython listings = [
    {"title": "Toyota Corolla 2018 - Low Mileage", "description": "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer.", "category": "Cars", "price": 28000000, "location": "Arusha", "condition": "Used"},
        {"title": "3 Bedroom House for Sale - Mikocheni", "description": "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room.", "category": "Houses", "price": 180000000, "location": "Dar es Salaam", "condition": "Used"},
            {"title": "MacBook Pro M2 - Perfect for Students", "description": "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals.", "category": "Laptops & Computers", "price": 2800000, "location": "Mwanza", "condition": "Used"},
                {"title": "Modern Sofa Set - 7 Seater", "description": "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room.", "category": "Furniture", "price": 850000, "location": "Dodoma", "condition": "Used"},
                    {"title": "Honda CB 150R - Sport Motorcycle", "description": "Honda CB 150R in excellent condition. Low mileage, well maintained.", "category": "Motorcycles", "price": 4500000, "location": "Mbeya", "condition": "Used"}
                    ]
2025-06-20 10:38:30,378 INFO ipython for data in listings:
        listing = frappe.new_doc("Listing")
            listing.title = data["title"]
2025-06-20 10:38:30,378 INFO ipython     listing.description = data["description"]
2025-06-20 10:38:30,379 INFO ipython     listing.category = data["category"]
2025-06-20 10:38:30,379 INFO ipython     listing.price = data["price"]
2025-06-20 10:38:30,379 INFO ipython     listing.location = data["location"]
2025-06-20 10:38:30,379 INFO ipython     listing.condition = data["condition"]
2025-06-20 10:38:30,379 INFO ipython     listing.contact_phone = "+255 700 000 000"
2025-06-20 10:38:30,379 INFO ipython     listing.contact_email = "<EMAIL>"
2025-06-20 10:38:30,379 INFO ipython     listing.status = "Active"
2025-06-20 10:38:30,380 INFO ipython     listing.listing_type = "For Sale"
2025-06-20 10:38:30,380 INFO ipython     listing.insert(ignore_permissions=True)
2025-06-20 10:38:30,380 INFO ipython     print(f"Created: {listing.name} - {listing.title}")
2025-06-20 10:38:30,380 INFO ipython print("All listings created!")
2025-06-20 10:38:30,380 INFO ipython # Create Toyota listing
2025-06-20 10:38:30,380 INFO ipython listing2 = frappe.new_doc("Listing")
2025-06-20 10:38:30,380 INFO ipython listing2.title = "Toyota Corolla 2018 - Low Mileage"
2025-06-20 10:38:30,380 INFO ipython listing2.description = "Well maintained Toyota Corolla 2018 with only 45,000 km on the odometer. Regular service history available."
2025-06-20 10:38:30,380 INFO ipython listing2.category = "Cars"
2025-06-20 10:38:30,381 INFO ipython listing2.price = 28000000
2025-06-20 10:38:30,381 INFO ipython listing2.location = "Arusha"
2025-06-20 10:38:30,381 INFO ipython listing2.condition = "Used"
2025-06-20 10:38:30,381 INFO ipython listing2.contact_phone = "+255754987654"
2025-06-20 10:38:30,381 INFO ipython listing2.contact_email = "<EMAIL>"
2025-06-20 10:38:30,381 INFO ipython listing2.status = "Active"
2025-06-20 10:38:30,381 INFO ipython listing2.listing_type = "For Sale"
2025-06-20 10:38:30,381 INFO ipython listing2.insert(ignore_permissions=True)
2025-06-20 10:38:30,381 INFO ipython print(f"Created: {listing2.name} - {listing2.title}")
2025-06-20 10:38:30,382 INFO ipython # Create House listing
2025-06-20 10:38:30,382 INFO ipython listing3 = frappe.new_doc("Listing")
2025-06-20 10:38:30,382 INFO ipython listing3.title = "3 Bedroom House for Sale - Mikocheni"
2025-06-20 10:38:30,382 INFO ipython listing3.description = "Beautiful 3 bedroom house in Mikocheni area. Modern kitchen, spacious living room, 2 bathrooms, and a small garden."
2025-06-20 10:38:30,382 INFO ipython listing3.category = "Houses"
2025-06-20 10:38:30,382 INFO ipython listing3.price = 180000000
2025-06-20 10:38:30,382 INFO ipython listing3.location = "Dar es Salaam"
2025-06-20 10:38:30,383 INFO ipython listing3.condition = "Used"
2025-06-20 10:38:30,383 INFO ipython listing3.contact_phone = "+255765432109"
2025-06-20 10:38:30,383 INFO ipython listing3.contact_email = "<EMAIL>"
2025-06-20 10:38:30,383 INFO ipython listing3.status = "Active"
2025-06-20 10:38:30,383 INFO ipython listing3.listing_type = "For Sale"
2025-06-20 10:38:30,383 INFO ipython listing3.insert(ignore_permissions=True)
2025-06-20 10:38:30,383 INFO ipython print(f"Created: {listing3.name} - {listing3.title}")
2025-06-20 10:38:30,383 INFO ipython # Create MacBook listing
2025-06-20 10:38:30,384 INFO ipython listing4 = frappe.new_doc("Listing")
2025-06-20 10:38:30,384 INFO ipython listing4.title = "MacBook Pro M2 - Perfect for Students"
2025-06-20 10:38:30,384 INFO ipython listing4.description = "MacBook Pro with M2 chip, 16GB RAM, 512GB SSD. Perfect for students and professionals. Comes with laptop bag."
2025-06-20 10:38:30,384 INFO ipython listing4.category = "Laptops & Computers"
2025-06-20 10:38:30,384 INFO ipython listing4.price = 2800000
2025-06-20 10:38:30,384 INFO ipython listing4.location = "Mwanza"
2025-06-20 10:38:30,384 INFO ipython listing4.condition = "Used"
2025-06-20 10:38:30,384 INFO ipython listing4.contact_phone = "+255678123456"
2025-06-20 10:38:30,384 INFO ipython listing4.contact_email = "<EMAIL>"
2025-06-20 10:38:30,384 INFO ipython listing4.status = "Active"
2025-06-20 10:38:30,385 INFO ipython listing4.listing_type = "For Sale"
2025-06-20 10:38:30,385 INFO ipython listing4.insert(ignore_permissions=True)
2025-06-20 10:38:30,385 INFO ipython print(f"Created: {listing4.name} - {listing4.title}")
2025-06-20 10:38:30,385 INFO ipython # Create Sofa listing
2025-06-20 10:38:30,385 INFO ipython listing5 = frappe.new_doc("Listing")
2025-06-20 10:38:30,385 INFO ipython listing5.title = "Modern Sofa Set - 7 Seater"
2025-06-20 10:38:30,385 INFO ipython listing5.description = "Beautiful modern sofa set with 7 seats. Comfortable and stylish, perfect for living room. Excellent condition."
2025-06-20 10:38:30,385 INFO ipython listing5.category = "Furniture"
2025-06-20 10:38:30,385 INFO ipython listing5.price = 850000
2025-06-20 10:38:30,386 INFO ipython listing5.location = "Dodoma"
2025-06-20 10:38:30,386 INFO ipython listing5.condition = "Used"
2025-06-20 10:38:30,386 INFO ipython listing5.contact_phone = "+255689765432"
2025-06-20 10:38:30,386 INFO ipython listing5.contact_email = "<EMAIL>"
2025-06-20 10:38:30,386 INFO ipython listing5.status = "Active"
2025-06-20 10:38:30,386 INFO ipython listing5.listing_type = "For Sale"
2025-06-20 10:38:30,386 INFO ipython listing5.insert(ignore_permissions=True)
2025-06-20 10:38:30,386 INFO ipython print(f"Created: {listing5.name} - {listing5.title}")
2025-06-20 10:38:30,386 INFO ipython print("All test listings created successfully!")
2025-06-20 10:38:30,387 INFO ipython # Verify listings were created
2025-06-20 10:38:30,387 INFO ipython listings = frappe.db.get_list("Listing", fields=["name", "title", "price", "location", "status"])
2025-06-20 10:38:30,387 INFO ipython print(f"Total listings created: {len(listings)}")
2025-06-20 10:38:30,387 INFO ipython for listing in listings:
        print(f"- {listing.name}: {listing.title} - {listing.price:,} TZS in {listing.location}")
        
2025-06-20 10:38:30,387 INFO ipython === session end ===
2025-06-20 10:39:33,781 INFO ipython === bench console session ===
2025-06-20 10:39:33,782 INFO ipython import frappe
2025-06-20 10:39:33,782 INFO ipython from datetime import datetime, timedelta
2025-06-20 10:39:33,782 INFO ipython # Update all listings to have expires_on date
2025-06-20 10:39:33,782 INFO ipython listings = frappe.get_all("Listing", fields=["name"])
2025-06-20 10:39:33,782 INFO ipython for listing in listings:
        doc = frappe.get_doc("Listing", listing.name)
            if not doc.expires_on:
                        doc.expires_on = (datetime.now() + timedelta(days=30)).date()
2025-06-20 10:39:33,783 INFO ipython         doc.save(ignore_permissions=True)
2025-06-20 10:39:33,783 INFO ipython         print(f"Updated {listing.name} with expires_on: {doc.expires_on}")
2025-06-20 10:39:33,783 INFO ipython print("All listings updated with expires_on dates")
2025-06-20 10:39:33,783 INFO ipython # Fix the listings one by one
2025-06-20 10:39:33,783 INFO ipython listings = frappe.get_all("Listing", fields=["name"])
2025-06-20 10:39:33,783 INFO ipython for listing in listings:
        doc = frappe.get_doc("Listing", listing.name)
            if not doc.expires_on:
                        doc.expires_on = (datetime.now() + timedelta(days=30)).date()
2025-06-20 10:39:33,783 INFO ipython         doc.save(ignore_permissions=True)
2025-06-20 10:39:33,784 INFO ipython         print(f"Updated {listing.name} with expires_on: {doc.expires_on}")
2025-06-20 10:39:33,784 INFO ipython     else:
            print(f"{listing.name} already has expires_on: {doc.expires_on}")
2025-06-20 10:39:33,784 INFO ipython print("Done updating listings")
2025-06-20 10:39:33,784 INFO ipython # Update listings directly with SQL
2025-06-20 10:39:33,784 INFO ipython frappe.db.sql("UPDATE `tabListing` SET expires_on = DATE_ADD(CURDATE(), INTERVAL 30 DAY) WHERE expires_on IS NULL")
2025-06-20 10:39:33,784 INFO ipython frappe.db.commit()
2025-06-20 10:39:33,784 INFO ipython print("Updated all listings with expires_on dates")
2025-06-20 10:39:33,784 INFO ipython # Test the API
2025-06-20 10:39:33,784 INFO ipython response = frappe.call("dmsoko.api.listings.get_recent_listings", limit=5)
2025-06-20 10:39:33,785 INFO ipython print(f"API Response: {response}")
2025-06-20 10:39:33,785 INFO ipython === session end ===
